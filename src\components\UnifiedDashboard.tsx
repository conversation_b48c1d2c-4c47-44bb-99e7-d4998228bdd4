import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  Plus, 
  Calendar, 
  Clock, 
  CheckCircle, 
  BarChart3, 
  Sparkles, 
  TrendingUp, 
  Users, 
  Zap, 
  ArrowUpRight, 
  Star, 
  Target,
  Home,
  FileText,
  Settings as SettingsIcon,
  LogOut,
  Menu,
  X,
  Bell,
  Search
} from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import AuthGuard from "./AuthGuard";
import Analytics from "./Analytics";
import CreatePostModal from "./CreatePostModal";
import MyPosts from "./MyPosts";
import Settings from "./Settings";

const UnifiedDashboardContent = () => {
  const [activeView, setActiveView] = useState("overview");
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [posts, setPosts] = useState<any[]>([]);
  const [postHistory, setPostHistory] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const { signOut, user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  const sidebarItems = [
    { id: "overview", label: "Overview", icon: Home, description: "Dashboard home" },
    { id: "create", label: "Create", icon: Plus, description: "Create new content" },
    { id: "posts", label: "My Posts", icon: FileText, description: "Manage your posts" },
    { id: "analytics", label: "Analytics", icon: BarChart3, description: "View performance" },
    { id: "settings", label: "Settings", icon: SettingsIcon, description: "Account settings" },
  ];

  const quickActions = [
    {
      title: "Create Post",
      description: "Generate AI-powered content",
      icon: Plus,
      action: () => setShowCreateModal(true),
      color: "from-purple-500 to-blue-500",
      bgColor: "bg-purple-50"
    },
    {
      title: "Schedule Content",
      description: "Plan your posting calendar",
      icon: Calendar,
      action: () => setActiveView("posts"),
      color: "from-blue-500 to-cyan-500",
      bgColor: "bg-blue-50"
    },
    {
      title: "View Analytics",
      description: "Track your performance",
      icon: TrendingUp,
      action: () => setActiveView("analytics"),
      color: "from-green-500 to-emerald-500",
      bgColor: "bg-green-50"
    }
  ];

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      // Skip database calls for testing - use mock data
      console.log('Skipping database calls for testing mode');
      setPosts([]);
      setPostHistory([]);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      toast({
        title: "Signed out successfully",
        description: "You've been logged out of your account.",
      });
      navigate('/');
    } catch (error) {
      toast({
        title: "Error signing out",
        description: "There was a problem signing you out.",
        variant: "destructive",
      });
    }
  };

  const handleSidebarItemClick = (itemId: string) => {
    if (itemId === "create") {
      setShowCreateModal(true);
    } else {
      setActiveView(itemId);
    }
  };

  const renderMainContent = () => {
    switch (activeView) {
      case "overview":
        return <OverviewContent posts={posts} postHistory={postHistory} quickActions={quickActions} />;
      case "posts":
        return <MyPosts />;
      case "analytics":
        return <Analytics />;
      case "settings":
        return <Settings />;
      default:
        return <OverviewContent posts={posts} postHistory={postHistory} quickActions={quickActions} />;
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Top Header */}
      <header className="sticky top-0 z-40 bg-white/95 backdrop-blur-xl border-b border-gray-100 shadow-sm">
        <div className="flex items-center justify-between h-16 px-6">
          {/* Logo and Menu Toggle */}
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              className="p-2"
            >
              <Menu className="h-5 w-5" />
            </Button>
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 gradient-primary rounded-lg flex items-center justify-center">
                <Sparkles className="w-5 h-5 text-white" />
              </div>
              <span className="text-lg font-bold text-gray-900">
                ScribeSchedule
              </span>
            </div>
          </div>

          {/* Search and Actions */}
          <div className="flex items-center space-x-4">
            <div className="relative hidden md:block">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search posts, analytics..."
                className="pl-10 pr-4 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>
            <Button variant="ghost" size="sm" className="relative">
              <Bell className="h-5 w-5" />
              <Badge className="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-red-500 text-white text-xs flex items-center justify-center p-0">
                3
              </Badge>
            </Button>
            <Button
              onClick={() => setShowCreateModal(true)}
              className="btn-primary"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create
            </Button>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Modern Sidebar */}
        <aside className={`${sidebarCollapsed ? 'w-16' : 'w-64'} bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900 min-h-screen transition-all duration-300 relative`}>
          {/* Gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-600/10 via-transparent to-purple-600/10"></div>

          <div className="relative z-10 p-4 space-y-3">
            {/* User Profile Section */}
            {!sidebarCollapsed && (
              <div className="mb-6 p-3 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {user?.email?.charAt(0).toUpperCase() || 'P'}
                    </span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-white font-medium text-sm truncate">Personal</p>
                    <p className="text-gray-300 text-xs">Owner</p>
                  </div>
                </div>
              </div>
            )}

            {/* Navigation Items */}
            <div className="space-y-2">
              {sidebarItems.map((item) => {
                const Icon = item.icon;
                const isActive = activeView === item.id;
                return (
                  <Button
                    key={item.id}
                    variant="ghost"
                    onClick={() => handleSidebarItemClick(item.id)}
                    className={`w-full justify-start transition-all duration-300 rounded-xl border-0 ${
                      isActive
                        ? "bg-white/20 text-white shadow-lg backdrop-blur-sm border border-white/30"
                        : "text-gray-300 hover:text-white hover:bg-white/10"
                    } ${sidebarCollapsed ? 'px-3 py-3' : 'px-4 py-3'}`}
                  >
                    <Icon className={`h-5 w-5 ${sidebarCollapsed ? '' : 'mr-3'}`} />
                    {!sidebarCollapsed && (
                      <div className="flex flex-col items-start">
                        <span className="font-medium text-sm">{item.label}</span>
                        {item.description && (
                          <span className="text-xs opacity-75">{item.description}</span>
                        )}
                      </div>
                    )}
                  </Button>
                );
              })}
            </div>

            {/* Bottom Section */}
            <div className="absolute bottom-4 left-4 right-4">
              <div className="space-y-3">
                {/* Integrations */}
                {!sidebarCollapsed && (
                  <div className="p-3 bg-white/5 rounded-xl border border-white/10">
                    <p className="text-gray-400 text-xs font-medium mb-2">INTEGRATIONS</p>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2 text-gray-300 text-sm">
                        <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                        <span>Settings</span>
                      </div>
                      <div className="flex items-center space-x-2 text-gray-300 text-sm">
                        <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                        <span>Billing</span>
                      </div>
                    </div>
                  </div>
                )}

                {/* Sign Out Button */}
                <Button
                  variant="ghost"
                  onClick={handleSignOut}
                  className="w-full justify-start text-red-400 hover:bg-red-500/20 hover:text-red-300 rounded-xl border-0"
                >
                  <LogOut className={`h-4 w-4 ${sidebarCollapsed ? '' : 'mr-2'}`} />
                  {!sidebarCollapsed && 'Sign Out'}
                </Button>
              </div>
            </div>
          </div>
        </aside>

        {/* Main Content */}
        <main className="flex-1 bg-gray-50">
          {renderMainContent()}
        </main>
      </div>

      {/* Create Post Modal */}
      <CreatePostModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onOpenSettings={() => setActiveView("settings")}
        onPostSuccess={() => {
          // Refresh dashboard data after successful post
          fetchDashboardData();
        }}
      />
    </div>
  );
};

// Overview Content Component
const OverviewContent = ({ posts, postHistory, quickActions }: any) => {
  const upcomingPosts = posts.filter((post: any) => post.status === 'scheduled').slice(0, 5);
  const totalPosts = posts.length;
  const publishedPosts = postHistory.length;
  const scheduledPosts = posts.filter((post: any) => post.status === 'scheduled').length;

  return (
    <div className="p-6 space-y-8">
      {/* Modern Welcome Section */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 rounded-2xl p-8 text-white relative overflow-hidden">
        <div className="absolute top-0 right-0 w-40 h-40 bg-white/10 rounded-full transform translate-x-20 -translate-y-20"></div>
        <div className="absolute bottom-0 left-0 w-32 h-32 bg-white/5 rounded-full transform -translate-x-16 translate-y-16"></div>
        <div className="relative z-10">
          <h1 className="text-3xl font-bold mb-3">Welcome back! 👋</h1>
          <p className="text-blue-100 text-lg opacity-90">Ready to create amazing content today?</p>
        </div>
      </div>

      {/* Enhanced Quick Actions */}
      <div className="grid md:grid-cols-3 gap-6">
        <div className="group cursor-pointer bg-white rounded-2xl p-6 shadow-sm hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
          <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
          </div>
          <h3 className="text-xl font-bold text-gray-900 mb-2">Create Post</h3>
          <p className="text-gray-600">Generate AI-powered content</p>
        </div>

        <div className="group cursor-pointer bg-white rounded-2xl p-6 shadow-sm hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
          <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          <h3 className="text-xl font-bold text-gray-900 mb-2">Schedule Content</h3>
          <p className="text-gray-600">Plan your posting calendar</p>
        </div>

        <div className="group cursor-pointer bg-white rounded-2xl p-6 shadow-sm hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
          <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <h3 className="text-xl font-bold text-gray-900 mb-2">View Analytics</h3>
          <p className="text-gray-600">Track your performance</p>
        </div>
      </div>

      {/* Modern Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Total Posts */}
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-lg transition-all duration-300">
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <span className="bg-purple-100 text-purple-700 px-3 py-1 rounded-full text-xs font-medium">Total</span>
          </div>
          <div>
            <h3 className="text-3xl font-bold text-gray-900 mb-1">{totalPosts}</h3>
            <p className="text-gray-600 text-sm">Total Posts Created</p>
          </div>
        </div>

        {/* Published Posts */}
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-lg transition-all duration-300">
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <span className="bg-green-100 text-green-700 px-3 py-1 rounded-full text-xs font-medium">Live</span>
          </div>
          <div>
            <h3 className="text-3xl font-bold text-gray-900 mb-1">{publishedPosts}</h3>
            <p className="text-gray-600 text-sm">Published Posts</p>
          </div>
        </div>

        {/* Scheduled Posts */}
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-lg transition-all duration-300">
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <span className="bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-xs font-medium">Queue</span>
          </div>
          <div>
            <h3 className="text-3xl font-bold text-gray-900 mb-1">{scheduledPosts}</h3>
            <p className="text-gray-600 text-sm">Scheduled Posts</p>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100">
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
              <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h2 className="text-xl font-bold text-gray-900">Recent Activity</h2>
          </div>
        </div>

        <div className="p-6">
          {upcomingPosts.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No scheduled posts yet</h3>
              <p className="text-gray-600 mb-6">Create your first post to get started with your content strategy.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {upcomingPosts.map((post: any, index: number) => (
                <div key={post.id} className="p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors duration-200">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded-md text-xs font-medium">
                          {post.platform}
                        </span>
                        <span className="text-sm text-gray-500">
                          {post.scheduled_for ? new Date(post.scheduled_for).toLocaleDateString() : 'Draft'}
                        </span>
                      </div>
                      <p className="font-medium text-gray-900 mb-1 line-clamp-2">{post.content}</p>
                      <p className="text-sm text-gray-600">Status: {post.status}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 rounded-md text-xs font-medium ${
                        post.status === 'scheduled'
                          ? 'bg-green-100 text-green-700'
                          : 'bg-gray-100 text-gray-700'
                      }`}>
                        {post.status === 'scheduled' ? 'Scheduled' : 'Draft'}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Wrapper component without AuthGuard for testing
const UnifiedDashboard = () => {
  return <UnifiedDashboardContent />;
};

export default UnifiedDashboard;
