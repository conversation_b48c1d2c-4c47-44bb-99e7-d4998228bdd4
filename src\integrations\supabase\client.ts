// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://eqiuukwwpdiyncahrdny.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVxaXV1a3d3cGRpeW5jYWhyZG55Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkxOTA5MzcsImV4cCI6MjA2NDc2NjkzN30.sgwl7oP2fJD7rh64w59XWdfMCS0XQcNjD4Qr_WGILGs";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

export async function postToPlatform({ platform, content, subreddit, image }) {
  try {
    // Step 1: Construct request headers (no authentication needed)
    const headers = {
      'Content-Type': 'application/json',
    };

    // Step 2: Prepare request body
    const body = JSON.stringify({ platform, content, subreddit, image });

    // Step 3: Call the Edge Function
    const response = await fetch(`${SUPABASE_URL}/functions/v1/post-to-social`, {
      method: 'POST',
      headers,
      body,
    });

    // Step 5: Handle response
    const responseData = await response.json();
    if (!response.ok) {
      console.error('Post failed:', responseData.error || response.statusText);
      return { error: responseData.error || 'Failed to post' };
    }

    return { success: true, data: responseData };

  } catch (err) {
    console.error('Unexpected error:', err);
    return { error: 'Unexpected error occurred' };
  }
}