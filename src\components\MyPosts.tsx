// Refactored MyPosts.tsx with typing & state fix
import { useState, useEffect, useMemo, useCallback } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { PostCard } from "@/components/posts/PostCard";
import { PostFilters } from "@/components/posts/PostFilters";
import { EmptyState } from "@/components/posts/EmptyState";
import { EditPostModal } from "@/components/posts/EditPostModal";
import { usePostManagement } from "@/hooks/usePostManagement";
import { FileText } from "lucide-react";
import {
  getStatusColor,
  getPlatformColor,
  filterPosts,
  groupPostsByStatus
} from "@/utils/postUtils";

export type TabType = "all" | "draft" | "scheduled" | "posted";

const MyPosts = () => {
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [platformFilter, setPlatformFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<TabType>("all");
  const [editingPost, setEditingPost] = useState<any>(null);

  const {
    posts,
    loading,
    fetchPosts,
    deletePost,
    editPost
  } = usePostManagement();

  useEffect(() => {
    fetchPosts();
  }, [fetchPosts]); // Now safe to depend on fetchPosts since it's memoized

  // Remove debug logging to reduce console noise

  const handleEditPost = useCallback((post: any) => {
    setEditingPost(post);
  }, []);

  const handleSaveEdit = useCallback(async (postId: string, updatedData: any) => {
    await editPost(postId, updatedData);
    // editPost already calls fetchPosts internally, no need to call it again
    setEditingPost(null);
  }, [editPost]);

  const handleDeletePost = useCallback(async (postId: string, isHistoryPost: boolean) => {
    await deletePost(postId, isHistoryPost);
    // fetchPosts is already called inside deletePost hook
  }, [deletePost]);



  const filteredPosts = useMemo(() =>
    filterPosts(posts, searchTerm, platformFilter, statusFilter),
    [posts, searchTerm, platformFilter, statusFilter]
  );

  const groupedPosts = useMemo(() =>
    groupPostsByStatus(filteredPosts),
    [filteredPosts]
  );

  const renderPosts = useCallback((type: TabType) => {
    const postGroup = type === "all" ? filteredPosts : groupedPosts[type];
    if (!postGroup || postGroup.length === 0) {
      return <EmptyState type={type} />;
    }
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
        {postGroup.map((post) => (
          <PostCard
            key={post.id}
            post={post}
            onEdit={handleEditPost}
            onDelete={handleDeletePost}
            getStatusColor={getStatusColor}
            getPlatformColor={getPlatformColor}
          />
        ))}
      </div>
    );
  }, [filteredPosts, groupedPosts, handleEditPost, handleDeletePost, getStatusColor, getPlatformColor]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your posts...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 relative overflow-hidden">
      {/* Modern Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-blue-100/40 to-purple-100/40 rounded-full blur-3xl transform translate-x-48 -translate-y-48"></div>
        <div className="absolute bottom-0 left-0 w-80 h-80 bg-gradient-to-tr from-indigo-100/40 to-cyan-100/40 rounded-full blur-3xl transform -translate-x-40 translate-y-40"></div>
      </div>

      <div className="relative z-10">
        {/* Modern Header Section */}
        <div className="bg-white/80 backdrop-blur-xl border-b border-gray-100/50 sticky top-0 z-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                    <FileText className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900">Posts</h1>
                    <p className="text-sm text-gray-500">Manage your content</p>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center space-x-3">
                <button className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white text-sm font-medium rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  Create with AI
                </button>
                <button className="inline-flex items-center px-4 py-2 bg-white text-gray-700 text-sm font-medium rounded-xl border border-gray-200 hover:bg-gray-50 transition-all duration-200 shadow-sm hover:shadow-md">
                  Create new
                </button>
              </div>
            </div>

            {/* Stats Row */}
            <div className="mt-6 grid grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{filteredPosts.length}</div>
                <div className="text-sm text-gray-500">Total Posts</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{groupedPosts.scheduled.length}</div>
                <div className="text-sm text-gray-500">Scheduled</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{groupedPosts.posted.length}</div>
                <div className="text-sm text-gray-500">Published</div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Filters Section */}
          <div className="mb-8">
            <PostFilters
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              platformFilter={platformFilter}
              setPlatformFilter={setPlatformFilter}
              statusFilter={statusFilter}
              setStatusFilter={(value: string) => setStatusFilter(value as TabType)}
            />
          </div>

          {/* Content Tabs */}
          <Tabs defaultValue="all" value={statusFilter} onValueChange={(val) => setStatusFilter(val as TabType)} className="space-y-6">
            <TabsList className="grid w-full max-w-md grid-cols-4 bg-white shadow-sm rounded-xl p-1 border border-gray-200">
              <TabsTrigger
                value="all"
                className="rounded-lg data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm transition-all duration-200 py-2 px-3 text-sm font-medium"
              >
                All
              </TabsTrigger>
              <TabsTrigger
                value="draft"
                className="rounded-lg data-[state=active]:bg-gray-600 data-[state=active]:text-white data-[state=active]:shadow-sm transition-all duration-200 py-2 px-3 text-sm font-medium"
              >
                Drafts
              </TabsTrigger>
              <TabsTrigger
                value="scheduled"
                className="rounded-lg data-[state=active]:bg-orange-500 data-[state=active]:text-white data-[state=active]:shadow-sm transition-all duration-200 py-2 px-3 text-sm font-medium"
              >
                Scheduled
              </TabsTrigger>
              <TabsTrigger
                value="posted"
                className="rounded-lg data-[state=active]:bg-green-600 data-[state=active]:text-white data-[state=active]:shadow-sm transition-all duration-200 py-2 px-3 text-sm font-medium"
              >
                Posted
              </TabsTrigger>
            </TabsList>

            {/* Posts Content */}
            <div className="mt-6">
              <TabsContent value="all" className="mt-0">{renderPosts("all")}</TabsContent>
              <TabsContent value="draft" className="mt-0">{renderPosts("draft")}</TabsContent>
              <TabsContent value="scheduled" className="mt-0">{renderPosts("scheduled")}</TabsContent>
              <TabsContent value="posted" className="mt-0">{renderPosts("posted")}</TabsContent>
            </div>
          </Tabs>
        </div>

        <EditPostModal
          isOpen={!!editingPost}
          onClose={() => setEditingPost(null)}
          post={editingPost}
          onSave={handleSaveEdit}
        />
      </div>
    </div>
  );
};

export default MyPosts;
