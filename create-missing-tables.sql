-- Run this SQL in your Supabase SQL Editor to create missing tables
-- This will fix the notification save error

-- Create user_preferences table for notification settings
CREATE TABLE IF NOT EXISTS user_preferences (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  email_notifications BOOLEA<PERSON> DEFAULT true,
  push_notifications B<PERSON><PERSON><PERSON>N DEFAULT false,
  marketing_notifications B<PERSON><PERSON>EA<PERSON> DEFAULT true,
  security_notifications BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id)
);

-- Enable RLS
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for user_preferences
CREATE POLICY "Users can view their own preferences" ON user_preferences
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own preferences" ON user_preferences
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own preferences" ON user_preferences
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own preferences" ON user_preferences
  FOR DELETE USING (auth.uid() = user_id);

-- Create profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS profiles (
  id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  full_name TEXT,
  country TEXT,
  gender TEXT,
  mobile_number TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on profiles
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for profiles
CREATE POLICY "Users can view their own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile" ON profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can delete their own profile" ON profiles
  FOR DELETE USING (auth.uid() = id);

-- Grant necessary permissions
GRANT ALL ON user_preferences TO authenticated;
GRANT ALL ON profiles TO authenticated;

-- Create function to upsert user preferences
CREATE OR REPLACE FUNCTION upsert_user_preferences(
  p_user_id UUID,
  p_email_notifications BOOLEAN,
  p_push_notifications BOOLEAN,
  p_marketing_notifications BOOLEAN,
  p_security_notifications BOOLEAN
) RETURNS VOID AS $$
BEGIN
  INSERT INTO user_preferences (
    user_id,
    email_notifications,
    push_notifications,
    marketing_notifications,
    security_notifications,
    updated_at
  ) VALUES (
    p_user_id,
    p_email_notifications,
    p_push_notifications,
    p_marketing_notifications,
    p_security_notifications,
    NOW()
  )
  ON CONFLICT (user_id) DO UPDATE SET
    email_notifications = EXCLUDED.email_notifications,
    push_notifications = EXCLUDED.push_notifications,
    marketing_notifications = EXCLUDED.marketing_notifications,
    security_notifications = EXCLUDED.security_notifications,
    updated_at = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
